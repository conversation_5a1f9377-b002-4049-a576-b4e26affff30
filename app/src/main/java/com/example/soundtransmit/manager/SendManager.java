package com.example.soundtransmit.manager;

import android.util.Log;

import com.example.soundtransmit.AudioSpeaker;
import com.example.soundtransmit.Constants;
import com.example.soundtransmit.Utils;
import com.example.soundtransmit.core.FeedbackSignal;
import com.example.soundtransmit.core.PreambleGen;
import com.example.soundtransmit.core.SymbolGeneration;
import com.example.soundtransmit.utils.LogUtils;

import java.util.Random;

public class SendManager extends Thread {

    private int m_attempt = 0;
    private int chirpLoopNumber = 0;

    private OnSenderListener onSenderListener;

    public SendManager(OnSenderListener onSenderListener) {
        this.onSenderListener = onSenderListener;
    }

    private void sendProbe() {
        short[] sig = PreambleGen.sounding_signal_s(); //用于生成和返回一个探测信道的前导信号
        //播放发送信号
        AudioSpeaker audioSpeaker = new AudioSpeaker(sig, Constants.fs, 0, sig.length);
        audioSpeaker.play();
        LogUtils.e("已发送探测信号");
    }

    private void doSendData(int messageId) {
        try {
            // 直接使用Constants中预定义的默认载波频率
            int[] valid_bins = Constants.valid_carrier_default;
            LogUtils.e("使用默认载波频率，数量: " + valid_bins.length);
            sendData(messageId, valid_bins, m_attempt); // 进行发送
        } catch (Exception e) {
            LogUtils.e("发送数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void run() {
        super.run();
        fire();
    }

    private void fire() {
        try {
            // 第一步：发送探测信号
            sendProbe();
            LogUtils.e("已发送探测信号，开始30秒循环发送");

            // 第二步：30秒循环发送数据信号
            long startTime = System.currentTimeMillis();
            long endTime = startTime + Constants.CURRENT_COUNTDOWN_TIME_MS;
            int currentMessageId = 1;

            while (System.currentTimeMillis() < endTime) {
                try {
                    // 发送当前消息
                    if (onSenderListener != null) {
                        String message = Constants.mmap.get(currentMessageId);
                        if (message == null) {
                            message = "未知消息ID: " + currentMessageId;
                        }
                        onSenderListener.onSenderMessage(message);
                    }

                    LogUtils.e("发送数据信号:" + currentMessageId);
                    doSendData(currentMessageId);

                    // 循环消息ID：1->2->3->...->10->1->2->...
                    currentMessageId++;
                    if (currentMessageId > 10) {
                        currentMessageId = 1;
                    }

                    // 等待1秒后发送下一条（如果还有时间的话）
                    if (System.currentTimeMillis() + 1000 < endTime) {
                        sleepMs(1000);
                    }
                } catch (Exception e) {
                    LogUtils.e("发送第" + currentMessageId + "条数据时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            LogUtils.e("30秒发送周期结束");
        } catch (Exception e) {
            LogUtils.e("发送端fire方法出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 确保清理资源
            cleanupResources();
        }
    }





    private void sendData(int messageId, int[] valid_bins, int m_attempt) {
        send_data_per(messageId, valid_bins, m_attempt);
    }

    private void send_data_per(int messageId, int[] valid_bins, int m_attempt) {
        // 计算比特数
        int msgbits = 16; // 初始化 msgbits 为 16，表示要发送的基本比特数量
        int traceDepth = 0; // traceDepth 是追踪深度，默认为 0，表示不添加额外比特
        msgbits += traceDepth;

        // 自适应发送
        send_data_helper(messageId, msgbits,
                valid_bins, m_attempt,
                Constants.SignalType.DataAdapt, Constants.ExpType.PER);
    }

    private void send_data_helper(int messageId, int numbits, int[] valid_bins, int m_attempt, Constants.SignalType sigType, Constants.ExpType expType) {

        short[] bits = SymbolGeneration.getCodedBits(messageId); // 获取待发送的编码比特序列。这个序列包含了数据编码后的比特信息

        // 将编码后的比特序列 bits 转换为音频信号 txsig。
        short[] txsig = SymbolGeneration.generateDataSymbols(bits, valid_bins, Constants.data_symreps, true, sigType, m_attempt);

        // 配置和播放音频信号
        AudioSpeaker audioSpeaker = new AudioSpeaker(txsig, Constants.fs, 0, txsig.length);
        audioSpeaker.play();

        // 计算信号播放时间并暂停执行确保传输完成
        int sleepTime = (int) (((double) txsig.length / Constants.fs) * 1000);
        try {
            Thread.sleep(sleepTime + Constants.SendPad);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public interface OnSenderListener {
        void onSenderMessage(String message);
        void onToastMessage(String toastMessage);
        void startCountdown();
    }
    /**
     * 清理资源，停止录音设备
     */
    private void cleanupResources() {
        try {
            if (Constants._OfflineRecorder != null) {
                Constants._OfflineRecorder.halt2();
                LogUtils.e("发送端已停止录音设备");
            }
        } catch (Exception e) {
            LogUtils.e("发送端清理资源时出错: " + e.getMessage());
        }
    }

    private void sleepMs(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
