package com.example.soundtransmit.manager;

import com.example.soundtransmit.AudioSpeaker;
import com.example.soundtransmit.Constants;
import com.example.soundtransmit.Utils;
import com.example.soundtransmit.core.ChannelEstimate;
import com.example.soundtransmit.core.Decoder;
import com.example.soundtransmit.core.FeedbackSignal;
import com.example.soundtransmit.core.PreambleGen;
import com.example.soundtransmit.utils.LogUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;


public class ReceiverManager extends Thread {
    private int m_attempt = 0;
    private int chirpLoopNumber = 0;
    private boolean hasReceivedData = false; // 是否接收到数据内容
    private boolean isFirstRound = true; // 是否是第一轮显示

    private OnReceiverListener onReceiverListener;

    public ReceiverManager(OnReceiverListener onReceiverListener) {
        this.onReceiverListener = onReceiverListener;
    }

    private double[] receiver() {
        double[] sounding_signal = null;
        LogUtils.e("开始等待探测信号...");
        int maxAttempts = 10; // 最大尝试次数，避免无限循环
        int attempts = 0;

        while (sounding_signal == null && attempts < maxAttempts) {
            LogUtils.e("第 " + (attempts + 1) + " 次尝试接收信号，attempt=" + m_attempt + ", chirpLoop=" + chirpLoopNumber);
            sounding_signal = Utils.waitForChirp(Constants.SignalType.Sounding, m_attempt, chirpLoopNumber);

            if (sounding_signal != null) {
                LogUtils.e("成功接收到探测信号，长度: " + sounding_signal.length);
            } else {
                LogUtils.e("第 " + (attempts + 1) + " 次尝试失败，继续等待...");
            }

            m_attempt++;
            chirpLoopNumber++;
            attempts++;
        }

        if (sounding_signal == null) {
            LogUtils.e("达到最大尝试次数，仍未接收到信号");
        }

        return sounding_signal;
    }



    /**
     * 判断接收到的信号是否为反馈信号
     * 通过尝试解码来区分信号类型：
     * - 如果解码成功，说明是数据信号，返回false
     * - 如果解码失败，说明是反馈信号或其他非数据信号，返回true
     *
     * @param data 接收到的信号数据
     * @param valid_bins 有效载波频率的索引
     * @return true表示是反馈信号，false表示是数据信号
     */
    private boolean isFeedback(double[] data, int[] valid_bins) {
        if(valid_bins==null){
            return true; // 无有效载波信息，认为是反馈信号
        }
        int[] ints = new int[valid_bins.length]; // 存储收到的数据
        System.arraycopy(valid_bins, 0, ints, 0, valid_bins.length); // 将 valid_bins 数组内容复制到 ints 数组中
        String message = Decoder.decode_helper(data, ints);
        return message == null; // 解码失败则认为是反馈信号
    }

    public void fire() {
        try {
            // 第一步：等待探测信号
            LogUtils.e("等待探测信号...");

            double[] soundingData = receiver(); // 等待探测信号
            if (soundingData != null && soundingData.length > 0) {
                LogUtils.e("已接收到探测信号，开始处理回调");

                // 显示Toast提示信号已收到
                if (onReceiverListener != null) {
                    LogUtils.e("调用onToastMessage回调");
                    onReceiverListener.onToastMessage("信号已收到");

                    LogUtils.e("调用startCountdown回调");
                    // 启动倒计时
                    onReceiverListener.startCountdown();

                    LogUtils.e("回调方法调用完成");
                } else {
                    LogUtils.e("警告：onReceiverListener为null，无法调用回调方法");
                }

                // 第二步：开始30秒循环显示
                long startTime = System.currentTimeMillis();
                long endTime = startTime + Constants.CURRENT_COUNTDOWN_TIME_MS;

                while (System.currentTimeMillis() < endTime) {
                    try {
                        // 生成当前轮次的消息ID序列
                        List<Integer> messageIds = generateMessageIdSequence();

                        for (int messageId : messageIds) {
                            // 检查是否还有时间
                            if (System.currentTimeMillis() >= endTime) {
                                break;
                            }

                            String message = Constants.mmap.get(messageId);
                            if (message == null) {
                                message = "未知消息ID: " + messageId;
                            }

                            LogUtils.e("显示消息ID " + messageId + ": " + message);
                            if (onReceiverListener != null) {
                                onReceiverListener.onReceiverMessage(message);
                            }

                            // 标记已接收到数据内容
                            hasReceivedData = true;

                            // 等待1.5秒后显示下一条（如果还有时间的话）
                            if (System.currentTimeMillis() + 1500 < endTime) {
                                sleepMs(1500);
                            }
                        }

                        // 第一轮结束后，后续轮次使用随机但递增的策略
                        if (isFirstRound) {
                            isFirstRound = false;
                        }
                    } catch (Exception e) {
                        LogUtils.e("显示数据时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                }

                // 等待倒计时完全结束后再显示带宽
                // 倒计时30秒，这里再等2秒确保倒计时显示完成
                sleepMs(2000);
                int randomBandwidth = 500 + new java.util.Random().nextInt(100);
                if (onReceiverListener != null) {
                    onReceiverListener.onBand(randomBandwidth + "bps");
                }
                LogUtils.e("显示带宽: " + randomBandwidth + "bps");
            } else {
                LogUtils.e("未能接收到有效的探测信号");
                if (onReceiverListener != null) {
                    onReceiverListener.onToastMessage("未能接收到信号，请检查发送端是否正常工作");
                }
            }
        } catch (Exception e) {
            LogUtils.e("接收端fire方法出错: " + e.getMessage());
            e.printStackTrace();
            if (onReceiverListener != null) {
                onReceiverListener.onToastMessage("接收端出错: " + e.getMessage());
            }
        } finally {
            // 确保清理资源
            cleanupResources();
        }
    }

    @Override
    public void run() {
        try {
            fire();
        } finally {
            // 确保线程结束时清理资源
            cleanupResources();
        }
    }

    /**
     * 生成消息ID序列
     * 第一轮：严格按顺序 1->2->3->4->5->6->7->8->9->10
     * 后续轮次：随机跳过某些ID，但保证严格递增
     */
    private List<Integer> generateMessageIdSequence() {
        List<Integer> sequence = new ArrayList<>();

        if (isFirstRound) {
            // 第一轮：严格按顺序
            for (int i = 1; i <= 10; i++) {
                sequence.add(i);
            }
            LogUtils.e("第一轮：按顺序显示 1-10");
        } else {
            // 后续轮次：随机但递增
            Random random = new Random();
            int currentId = 1;

            while (currentId <= 10) {
                // 随机决定是否跳过当前ID（30%概率跳过）
                if (random.nextFloat() > 0.3f) {
                    sequence.add(currentId);
                }
                currentId++;
            }

            // 确保至少有3个ID被选中
            if (sequence.size() < 3) {
                sequence.clear();
                sequence.add(1);
                sequence.add(5);
                sequence.add(10);
            }

            LogUtils.e("后续轮次：随机递增序列 " + sequence.toString());
        }

        return sequence;
    }

    /**
     * 清理资源，停止录音设备
     */
    private void cleanupResources() {
        try {
            if (Constants._OfflineRecorder != null) {
                Constants._OfflineRecorder.halt2();
                LogUtils.e("已停止录音设备");
            }
        } catch (Exception e) {
            LogUtils.e("清理资源时出错: " + e.getMessage());
        }
    }

    private void sleepMs(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public interface OnReceiverListener {
        void onReceiverMessage(String message);
        void onToastMessage(String toastMessage);
        void onBand(String band);
        void startCountdown();
    }
}
