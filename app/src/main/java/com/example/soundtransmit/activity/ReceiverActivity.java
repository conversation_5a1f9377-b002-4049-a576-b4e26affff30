package com.example.soundtransmit.activity;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.soundtransmit.Constants;
import com.example.soundtransmit.R;
import com.example.soundtransmit.manager.ReceiverManager;
import com.example.soundtransmit.utils.LogUtils;

public class ReceiverActivity extends AppCompatActivity implements ReceiverManager.OnReceiverListener {
    private TextView tvReceiverMessage;
    private TextView tvBand;
    private TextView tvCountdown;
    private CountDownTimer countDownTimer;
    private Handler handler = new Handler(Looper.getMainLooper());


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_receiver);
        initView();
        startReceiverAndRequestRecordAudioPermissions();
    }

    private void initView() {
        tvReceiverMessage = findViewById(R.id.tvReceiverMessage);
        tvBand = findViewById(R.id.tvBand);
        tvCountdown = findViewById(R.id.tvCountdown);
    }

    private void startReceiverAndRequestRecordAudioPermissions() {
        LogUtils.e("检查录音权限");
        if (checkSelfPermission(Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            LogUtils.e("录音权限未授予，请求权限");
            Toast.makeText(this, "需要录音权限才能接收信号", Toast.LENGTH_SHORT).show();
            requestPermissions(new String[]{Manifest.permission.RECORD_AUDIO}, 0);
        } else {
            LogUtils.e("录音权限已授予，启动接收端");
            startReceiver();
        }
    }

    private void startReceiver() {
        LogUtils.e("开始启动接收端");
        try {
            new ReceiverManager(this).start();
            LogUtils.e("接收端线程已启动");
        } catch (Exception e) {
            LogUtils.e("启动接收端失败: " + e.getMessage());
            handler.post(() -> Toast.makeText(this, "启动接收端失败: " + e.getMessage(), Toast.LENGTH_LONG).show());
        }
    }

    public void startCountdown() {
        LogUtils.e("收到启动倒计时请求");
        handler.post(() -> {
            try {
                if (countDownTimer != null) {
                    countDownTimer.cancel();
                    LogUtils.e("取消之前的倒计时");
                }

                LogUtils.e("开始创建新的倒计时，时长: " + (Constants.CURRENT_COUNTDOWN_TIME_MS / 1000) + "秒");
                countDownTimer = new CountDownTimer(Constants.CURRENT_COUNTDOWN_TIME_MS, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        long secondsRemaining = millisUntilFinished / 1000;
                        tvCountdown.setText(String.valueOf(secondsRemaining));
                        LogUtils.e("倒计时更新: " + secondsRemaining + "秒");
                    }

                    @Override
                    public void onFinish() {
                        tvCountdown.setText("0");
                        LogUtils.e("倒计时结束，显示0");
                    }
                };
                countDownTimer.start();
                LogUtils.e("倒计时已启动");
            } catch (Exception e) {
                LogUtils.e("启动倒计时失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        LogUtils.e("权限请求结果: requestCode=" + requestCode + ", permissions=" + java.util.Arrays.toString(permissions) + ", grantResults=" + java.util.Arrays.toString(grantResults));

        if (permissions.length > 0 && grantResults.length > 0) {
            if (requestCode == 0 && Manifest.permission.RECORD_AUDIO.equals(permissions[0])) {
                if (PackageManager.PERMISSION_GRANTED == grantResults[0]) {
                    LogUtils.e("录音权限已授予，启动接收端");
                    Toast.makeText(this, "权限已授予，开始接收", Toast.LENGTH_SHORT).show();
                    startReceiver();
                } else {
                    LogUtils.e("录音权限被拒绝");
                    Toast.makeText(this, "需要录音权限才能正常工作", Toast.LENGTH_LONG).show();
                }
            }
        }
    }

    @Override
    public void onReceiverMessage(String message) {
        LogUtils.e("收到消息: " + message);
        handler.post(() -> {
            try {
                tvReceiverMessage.append(message + "\n");
                // 自动滚动到底部
                ScrollView scrollView = (ScrollView) tvReceiverMessage.getParent();
                scrollView.fullScroll(ScrollView.FOCUS_DOWN);
                LogUtils.e("消息已显示到UI");
            } catch (Exception e) {
                LogUtils.e("显示消息失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    public void onToastMessage(String message) {
        LogUtils.e("收到Toast消息: " + message);
        handler.post(() -> {
            try {
                Toast.makeText(getApplicationContext(), message, Toast.LENGTH_SHORT).show();
                LogUtils.e("Toast已显示");
            } catch (Exception e) {
                LogUtils.e("显示Toast失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    public void onBand(String band) {
        LogUtils.e("收到带宽信息: " + band);
        handler.post(() -> {
            try {
                tvBand.setText(band);
                LogUtils.e("带宽信息已显示");
            } catch (Exception e) {
                LogUtils.e("显示带宽失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
