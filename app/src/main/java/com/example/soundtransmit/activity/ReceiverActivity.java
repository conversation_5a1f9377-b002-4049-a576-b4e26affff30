package com.example.soundtransmit.activity;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.soundtransmit.Constants;
import com.example.soundtransmit.R;
import com.example.soundtransmit.manager.ReceiverManager;

public class ReceiverActivity extends AppCompatActivity implements ReceiverManager.OnReceiverListener {
    private TextView tvReceiverMessage;
    private TextView tvBand;
    private TextView tvCountdown;
    private CountDownTimer countDownTimer;
    private Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_receiver);
        initView();
        startReceiverAndRequestRecordAudioPermissions();
    }

    private void initView() {
        tvReceiverMessage = findViewById(R.id.tvReceiverMessage);
        tvBand = findViewById(R.id.tvBand);
        tvCountdown = findViewById(R.id.tvCountdown);
    }

    private void startReceiverAndRequestRecordAudioPermissions() {
        if (checkSelfPermission(Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(new String[]{Manifest.permission.RECORD_AUDIO}, 0);
        } else {
            startReceiver();
        }
    }

    private void startReceiver() {
        new ReceiverManager(this).start();
    }

    public void startCountdown() {
        countDownTimer = new CountDownTimer(Constants.CURRENT_COUNTDOWN_TIME_MS, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long secondsRemaining = millisUntilFinished / 1000;
                tvCountdown.post(() -> tvCountdown.setText(String.valueOf(secondsRemaining)));
            }

            @Override
            public void onFinish() {
                tvCountdown.post(() -> tvCountdown.setText("0"));
            }
        };
        countDownTimer.start();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (permissions.length > 0 && grantResults.length > 0) {
            if (requestCode == 0 && Manifest.permission.RECORD_AUDIO.equals(permissions[0]) && PackageManager.PERMISSION_GRANTED == grantResults[0]) {
                startReceiver();
            }
        }
    }

    @Override
    public void onReceiverMessage(String message) {
        tvReceiverMessage.post(() -> tvReceiverMessage.append(message + "\n"));
    }

    @Override
    public void onToastMessage(String message) {
        handler.post(() -> Toast.makeText(getApplicationContext(), message, Toast.LENGTH_SHORT).show());
    }
    @Override
    public void onBand(String band) {
        tvBand.post(() -> tvBand.setText(band));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
