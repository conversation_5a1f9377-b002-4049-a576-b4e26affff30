package com.example.soundtransmit.activity;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.soundtransmit.Constants;
import com.example.soundtransmit.R;
import com.example.soundtransmit.manager.SendManager;
import com.example.soundtransmit.utils.LogUtils;

public class SenderActivity extends AppCompatActivity implements SendManager.OnSenderListener {
    private TextView tvSendMessage;
    private TextView tvCountdown;
    private CountDownTimer countDownTimer;
    private Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sender);
        initView();
    }

    private void initView() {
        tvSendMessage = findViewById(R.id.tvSendMessage);
        tvCountdown = findViewById(R.id.tvCountdown);

        findViewById(R.id.btSendMessage).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startSendAndRequestRecordAudioPermissions();
            }
        });
    }

    private void startSendAndRequestRecordAudioPermissions() {
        LogUtils.e("发送端检查权限");
        // 注意：发送端实际上不需要录音权限，只需要播放音频
        // 但为了保持与现有架构的兼容性，暂时保留权限检查
        if (checkSelfPermission(Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            LogUtils.e("录音权限未授予，请求权限");
            Toast.makeText(this, "需要录音权限（用于系统兼容性）", Toast.LENGTH_SHORT).show();
            requestPermissions(new String[]{Manifest.permission.RECORD_AUDIO}, 0);
        } else {
            LogUtils.e("权限已授予，启动发送端");
            startSender();
        }
    }

    private void startSender() {
        // 点击发送按钮立即开始倒计时
        startCountdown();
        // 启动发送管理器
        new SendManager(this).start();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (permissions.length > 0 && grantResults.length > 0) {
            if (requestCode == 0 && Manifest.permission.RECORD_AUDIO.equals(permissions[0]) && PackageManager.PERMISSION_GRANTED == grantResults[0]) {
                startSender();
            }
        }
    }

    @Override
    public void onToastMessage(String message) {
        handler.post(() -> Toast.makeText(getApplicationContext(), message, Toast.LENGTH_SHORT).show());
    }

    @Override
    public void onSenderMessage(String message) {
        tvSendMessage.post(() -> tvSendMessage.append(message + "\n"));
    }

    @Override
    public void startCountdown() {
        countDownTimer = new CountDownTimer(Constants.CURRENT_COUNTDOWN_TIME_MS, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long secondsRemaining = millisUntilFinished / 1000;
                handler.post(() -> tvCountdown.setText(String.valueOf(secondsRemaining)));
            }

            @Override
            public void onFinish() {
                handler.post(() -> tvCountdown.setText("0"));
            }
        };
        countDownTimer.start();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
