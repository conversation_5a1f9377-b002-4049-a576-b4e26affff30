package com.example.soundtransmit.activity;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.soundtransmit.Constants;
import com.example.soundtransmit.R;
import com.example.soundtransmit.manager.SendManager;
import com.example.soundtransmit.utils.LogUtils;
import com.example.soundtransmit.utils.LogUtils;

public class SenderActivity extends AppCompatActivity implements SendManager.OnSenderListener {
    private TextView tvSendMessage;
    private TextView tvCountdown;
    private CountDownTimer countDownTimer;
    private Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sender);
        initView();
    }

    private void initView() {
        tvSendMessage = findViewById(R.id.tvSendMessage);
        tvCountdown = findViewById(R.id.tvCountdown);

        findViewById(R.id.btSendMessage).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 发送端只需要播放音频，不需要录音权限
                startSender();
            }
        });
    }

    private void startSender() {
        LogUtils.e("开始启动发送端");
        try {
            // 点击发送按钮立即开始倒计时
            startCountdown();
            // 启动发送管理器
            new SendManager(this).start();
            LogUtils.e("发送端已启动");
        } catch (Exception e) {
            LogUtils.e("启动发送端失败: " + e.getMessage());
            handler.post(() -> Toast.makeText(this, "启动发送端失败: " + e.getMessage(), Toast.LENGTH_LONG).show());
        }
    }



    @Override
    public void onToastMessage(String message) {
        handler.post(() -> Toast.makeText(getApplicationContext(), message, Toast.LENGTH_SHORT).show());
    }

    @Override
    public void onSenderMessage(String message) {
        LogUtils.e("发送消息: " + message);
        handler.post(() -> {
            try {
                tvSendMessage.append(message + "\n");
                // 自动滚动到底部
                ScrollView scrollView = (ScrollView) tvSendMessage.getParent();
                scrollView.fullScroll(ScrollView.FOCUS_DOWN);
                LogUtils.e("消息已显示到UI");
            } catch (Exception e) {
                LogUtils.e("显示发送消息失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    public void startCountdown() {
        LogUtils.e("发送端启动倒计时");
        handler.post(() -> {
            try {
                if (countDownTimer != null) {
                    countDownTimer.cancel();
                    LogUtils.e("取消之前的倒计时");
                }

                LogUtils.e("开始创建新的倒计时，时长: " + (Constants.CURRENT_COUNTDOWN_TIME_MS / 1000) + "秒");
                countDownTimer = new CountDownTimer(Constants.CURRENT_COUNTDOWN_TIME_MS, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        long secondsRemaining = millisUntilFinished / 1000;
                        tvCountdown.setText(String.valueOf(secondsRemaining));
                        LogUtils.e("发送端倒计时更新: " + secondsRemaining + "秒");
                    }

                    @Override
                    public void onFinish() {
                        tvCountdown.setText("0");
                        LogUtils.e("发送端倒计时结束");
                    }
                };
                countDownTimer.start();
                LogUtils.e("发送端倒计时已启动");
            } catch (Exception e) {
                LogUtils.e("启动发送端倒计时失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
