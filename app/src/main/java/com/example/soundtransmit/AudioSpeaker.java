package com.example.soundtransmit;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.util.Log;

import com.example.soundtransmit.utils.ContextUtils;
import com.example.soundtransmit.utils.LogUtils;


public class AudioSpeaker {

    private AudioTrack audioTrack; // 用于播放音频的对象
    private short[] samples; // 存储音频样本的 short 类型数组
    private AudioManager audioManager; // 用于管理音频流和音量设置。
    int loops;
    int samplingFreq;

    int[] streams = new int[]{AudioManager.STREAM_MUSIC,
            AudioManager.STREAM_ACCESSIBILITY, AudioManager.STREAM_ALARM,
            AudioManager.STREAM_DTMF, AudioManager.STREAM_NOTIFICATION,
            AudioManager.STREAM_RING, AudioManager.STREAM_SYSTEM,
            AudioManager.STREAM_VOICE_CALL};

    int preamble_length; // 音频样本开始循环播放的位置

    /**
     * AudioSpeaker 构造函数，初始化音频播放环境并配置音频参数。
     *
     * @param samples         要写入的音频样本数组。
     * @param samplingFreq    音频采样频率，以赫兹为单位（例如 44100Hz 表示 CD 质量）。
     * @param loops           设置音轨循环播放的次数。
     * @param preamble_length 音频样本开始循环播放的位置。
     */
    public AudioSpeaker(short[] samples, int samplingFreq, int loops, int preamble_length) {
        this.loops = loops;
        this.preamble_length = preamble_length;
        this.samplingFreq = samplingFreq;

        audioManager = (AudioManager) ContextUtils.application.getSystemService(Context.AUDIO_SERVICE);
        for (Integer i : streams) {
            audioManager.adjustStreamVolume(i, AudioManager.ADJUST_MUTE, 0); // 静音
        }

        audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, AudioManager.ADJUST_UNMUTE, 0); // 取消静音
        write(samples);// 调用 write 方法将音频样本写入 AudioTrack 对象
    }

    public void write(short[] samples) {
        this.samples = samples;

        // 配置 AudioAttributes
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)  // 设置为媒体用途
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)  // 设置为音乐内容
                .build();

        // 配置 AudioFormat
        AudioFormat audioFormat = new AudioFormat.Builder()
                .setSampleRate(samplingFreq)
                .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                .build();

        // 创建 AudioTrack 实例
        audioTrack = new AudioTrack.Builder()
                .setAudioAttributes(audioAttributes)
                .setAudioFormat(audioFormat)
                .setBufferSizeInBytes(samples.length * 2)
                .setTransferMode(AudioTrack.MODE_STATIC)
                .build();

        // 向 AudioTrack 写入数据
        audioTrack.write(samples, 0, samples.length);
    }

    /**
     * 播放音频样本，并设置音量和循环播放点。
     */
    public void play() {
        try {
            if (audioTrack == null) {
                LogUtils.e("AudioTrack未初始化，无法播放");
                return;
            }

            LogUtils.e("开始播放音频，样本长度: " + samples.length + ", 循环次数: " + loops);
            audioTrack.setLoopPoints(preamble_length, samples.length, loops);   // 设置音轨的循环点：从 preamble_length 开始，到 samples.length 结束，循环播放 loops 次

            // 使用 AudioTrack 的 setVolume 方法，根据系统媒体音量设置音轨音量
            float volume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC) / (float) audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
            audioTrack.setVolume(volume);  // 将系统媒体音量比例应用到音轨
            LogUtils.e("设置音量: " + volume);

            audioTrack.play();
            LogUtils.e("音频播放已启动");
        } catch (Exception e) {
            LogUtils.e("音频播放失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
