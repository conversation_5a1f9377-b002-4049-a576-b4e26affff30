<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@android:color/darker_gray"
        android:gravity="center"
        android:text="接收端"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:textStyle="bold" />
    <!-- 固定高度的滑动窗口 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadeScrollbars="false"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/tvReceiverMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@android:color/black"
            android:textSize="16sp" />
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@android:color/holo_blue_light"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingEnd="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="带宽："
            android:textColor="@android:color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvBand"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text=""
            android:textColor="@android:color/holo_red_dark"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="倒计时: "
            android:textColor="@android:color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvCountdown"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="--"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="s"
            android:textColor="@android:color/black"
            android:textSize="14sp" />

    </LinearLayout>
</LinearLayout>
